<?php

require_once 'vendor/autoload.php';

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env');

// Créer le kernel Symfony
$kernel = new Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Récupérer les services nécessaires
$entityManager = $container->get('doctrine.orm.entity_manager');
$documentRepository = $entityManager->getRepository('App\Entity\Document');

echo "=== TEST DE LA PAGE STATISTIQUES (GOULOTS D'ÉTRANGLEMENT) ===\n\n";

// 1. Tester la méthode utilisée par StatisticsController
echo "1. TEST DocumentRepository::getAverageTimeByState() (utilisée par StatisticsController):\n";
$averageTimeByState = $documentRepository->getAverageTimeByState();
echo "   Nombre d'états avec données: " . count($averageTimeByState) . "\n";
foreach ($averageTimeByState as $state => $avgTime) {
    echo "   - {$state}: {$avgTime} jours\n";
}

// 2. Simuler la méthode getBottlenecks du contrôleur
echo "\n2. SIMULATION de la méthode getBottlenecks() du StatisticsController:\n";
function getBottlenecks($averageTimeByState) {
    // Prendre les 5 états avec les temps moyens les plus longs
    return array_slice($averageTimeByState, 0, 5, true);
}

$bottlenecks = getBottlenecks($averageTimeByState);
echo "   Top 5 goulots d'étranglement (comme affiché sur la page):\n";
foreach ($bottlenecks as $state => $time) {
    echo "   - {$state}: {$time} jours\n";
}

// 3. Vérifier si Project apparaît encore
echo "\n3. VÉRIFICATION spécifique pour 'Project':\n";
if (isset($bottlenecks['Project'])) {
    echo "   ❌ PROBLÈME: 'Project' apparaît encore avec {$bottlenecks['Project']} jours\n";
} else {
    echo "   ✅ CORRECT: 'Project' n'apparaît plus dans les goulots\n";
}

// 4. Vérifier les autres états problématiques mentionnés
$problematicStates = ['Methode_assemblage', 'Qual_Logistique', 'Assembly', 'Quality'];
echo "\n4. VÉRIFICATION des autres états problématiques:\n";
foreach ($problematicStates as $state) {
    if (isset($bottlenecks[$state])) {
        echo "   ❌ {$state}: {$bottlenecks[$state]} jours (encore présent)\n";
    } else {
        echo "   ✅ {$state}: absent des goulots (correct)\n";
    }
}

// 5. Analyser pourquoi certains états pourraient encore avoir des valeurs élevées
echo "\n5. ANALYSE DÉTAILLÉE des états avec temps > 100 jours:\n";
$highTimeStates = array_filter($averageTimeByState, function($time) {
    return $time > 100;
});

if (count($highTimeStates) > 0) {
    echo "   États avec temps > 100 jours trouvés:\n";
    foreach ($highTimeStates as $state => $time) {
        echo "   - {$state}: {$time} jours\n";
        
        // Analyser les données sources pour cet état
        $times = $documentRepository->getProcessingTimesForState($state);
        if (count($times) > 0) {
            echo "     * Échantillon: " . count($times) . " mesures\n";
            echo "     * Min: " . min($times) . " jours\n";
            echo "     * Max: " . max($times) . " jours\n";
            echo "     * Médiane: " . (count($times) > 0 ? $times[intval(count($times)/2)] : 0) . " jours\n";
        } else {
            echo "     * Aucune donnée de temps individuel trouvée\n";
        }
    }
} else {
    echo "   ✅ Aucun état avec temps > 100 jours\n";
}

// 6. Comparer avec l'ancienne méthode pour voir la différence
echo "\n6. COMPARAISON avec l'ancienne méthode (pour diagnostic):\n";
$conn = $entityManager->getConnection();

// Simuler l'ancienne méthode pour quelques états
$testStates = ['Project', 'Quality', 'Assembly'];
foreach ($testStates as $state) {
    echo "   État '{$state}':\n";
    
    // Ancienne méthode (problématique)
    $oldSql = "
        SELECT 
            COUNT(*) as count,
            AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time
        FROM document d
        INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
        INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
        WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
        AND v2.date_visa >= v1.date_visa
    ";
    
    $oldResult = $conn->executeQuery($oldSql, ['$."' . $state . '"'])->fetchAssociative();
    $oldAvg = $oldResult['avg_time'] ? round($oldResult['avg_time'], 1) : 0;
    $oldCount = $oldResult['count'];
    
    // Nouvelle méthode
    $newAvg = $averageTimeByState[$state] ?? 0;
    $newTimes = $documentRepository->getProcessingTimesForState($state);
    $newCount = count($newTimes);
    
    echo "     - Ancienne méthode: {$oldAvg} jours (échantillon: {$oldCount})\n";
    echo "     - Nouvelle méthode: {$newAvg} jours (échantillon: {$newCount})\n";
    echo "     - Différence: " . ($oldAvg - $newAvg) . " jours\n";
}

// 7. Vérifier la cohérence des données
echo "\n7. VÉRIFICATION DE LA COHÉRENCE:\n";
$totalStatesInTimestamps = 0;
$statesWithExitTimestamps = 0;

$sql = "
    SELECT d.state_timestamps
    FROM document d
    WHERE d.state_timestamps IS NOT NULL 
    AND d.state_timestamps != '{}'
    AND d.state_timestamps != ''
    LIMIT 100
";

$result = $conn->executeQuery($sql);
$sampleDocs = $result->fetchAllAssociative();

foreach ($sampleDocs as $docData) {
    $timestamps = json_decode($docData['state_timestamps'], true);
    if (is_array($timestamps)) {
        foreach ($timestamps as $state => $entries) {
            $totalStatesInTimestamps++;
            if (is_array($entries)) {
                foreach ($entries as $entry) {
                    if (isset($entry['exit']) && $entry['exit'] !== null) {
                        $statesWithExitTimestamps++;
                        break; // Un seul exit par état suffit
                    }
                }
            }
        }
    }
}

$exitRate = $totalStatesInTimestamps > 0 ? round(($statesWithExitTimestamps / $totalStatesInTimestamps) * 100, 1) : 0;
echo "   - États avec timestamps de sortie: {$exitRate}% ({$statesWithExitTimestamps}/{$totalStatesInTimestamps})\n";

if ($exitRate < 50) {
    echo "   ⚠️  ATTENTION: Peu d'états ont des timestamps de sortie complets\n";
    echo "   📝 RECOMMANDATION: Améliorer l'enregistrement des timestamps de sortie\n";
} else {
    echo "   ✅ Bonne couverture des timestamps de sortie\n";
}

// 8. Conclusion
echo "\n8. CONCLUSION:\n";
if (count($averageTimeByState) < 3) {
    echo "   ⚠️  Peu d'états ont des données calculées (" . count($averageTimeByState) . " états)\n";
    echo "   📝 Cela peut expliquer pourquoi certains goulots n'apparaissent plus\n";
} else {
    echo "   ✅ Nombre raisonnable d'états avec données (" . count($averageTimeByState) . " états)\n";
}

$maxTime = count($averageTimeByState) > 0 ? max($averageTimeByState) : 0;
if ($maxTime > 100) {
    echo "   ❌ Des temps très élevés persistent (max: {$maxTime} jours)\n";
    echo "   📝 Vérifier la qualité des données de timestamps\n";
} else {
    echo "   ✅ Temps moyens dans des plages raisonnables (max: {$maxTime} jours)\n";
}

echo "\n=== FIN DU TEST ===\n";
