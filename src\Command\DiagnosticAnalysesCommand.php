<?php

namespace App\Command;

use App\Repository\DocumentRepository;
use App\Service\DataAnalysisService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:diagnostic-analyses',
    description: 'Diagnostic des analyses et prédictions'
)]
class DiagnosticAnalysesCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private DocumentRepository $documentRepository,
        private DataAnalysisService $dataAnalysisService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $conn = $this->entityManager->getConnection();

        $io->title('DIAGNOSTIC DES ANALYSES ET PRÉDICTIONS');

        // 1. Vérifier le nombre total de documents
        $totalDocuments = $this->documentRepository->count([]);
        $io->section("1. NOMBRE TOTAL DE DOCUMENTS: {$totalDocuments}");

        // 2. Analyser les documents avec visas BE_0 et Costing
        $sql = "
            SELECT 
                COUNT(DISTINCT d.id) as total_documents,
                COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL THEN d.id END) as with_be0_visa,
                COUNT(DISTINCT CASE WHEN v2.id IS NOT NULL THEN d.id END) as with_costing_visa,
                COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL AND v2.id IS NOT NULL THEN d.id END) as with_both_visas
            FROM document d
            LEFT JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            LEFT JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
        ";

        $result = $conn->executeQuery($sql)->fetchAssociative();
        $io->section('2. ANALYSE DES VISAS');
        $io->listing([
            "Documents avec visa BE_0: {$result['with_be0_visa']}",
            "Documents avec visa Costing: {$result['with_costing_visa']}",
            "Documents avec les deux visas: {$result['with_both_visas']}"
        ]);

        // 3. Statistiques générales des temps de traitement
        $sql = "
            SELECT 
                COUNT(*) as count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time,
                MIN(DATEDIFF(v2.date_visa, v1.date_visa)) as min_time,
                MAX(DATEDIFF(v2.date_visa, v1.date_visa)) as max_time,
                STDDEV(DATEDIFF(v2.date_visa, v1.date_visa)) as std_dev
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
            AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
        ";

        $stats = $conn->executeQuery($sql)->fetchAssociative();
        $io->section('3. STATISTIQUES GÉNÉRALES DES TEMPS DE TRAITEMENT');
        $io->listing([
            "Nombre d'échantillons: {$stats['count']}",
            "Temps moyen: " . round($stats['avg_time'], 2) . " jours",
            "Temps minimum: {$stats['min_time']} jours",
            "Temps maximum: {$stats['max_time']} jours",
            "Écart-type: " . round($stats['std_dev'], 2) . " jours"
        ]);

        // 4. Analyser par type de document
        $sql = "
            SELECT 
                d.doc_type,
                COUNT(*) as count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time,
                MIN(DATEDIFF(v2.date_visa, v1.date_visa)) as min_time,
                MAX(DATEDIFF(v2.date_visa, v1.date_visa)) as max_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
            AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
            GROUP BY d.doc_type
            ORDER BY avg_time DESC
        ";

        $docTypeStats = $conn->executeQuery($sql)->fetchAllAssociative();
        $io->section('4. STATISTIQUES PAR TYPE DE DOCUMENT');
        $docTypeList = [];
        foreach ($docTypeStats as $stat) {
            $docTypeList[] = "{$stat['doc_type']}: {$stat['count']} docs, " . round($stat['avg_time'], 1) . " jours (min: {$stat['min_time']}, max: {$stat['max_time']})";
        }
        $io->listing($docTypeList);

        // 5. Analyser les tendances mensuelles
        $sql = "
            SELECT 
                DATE_FORMAT(v1.date_visa, '%m/%Y') as period_key,
                COUNT(*) as document_count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_processing_time,
                SUM(DATEDIFF(v2.date_visa, v1.date_visa)) as total_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
            AND v1.date_visa >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(v1.date_visa, '%m/%Y')
            ORDER BY period_key DESC
            LIMIT 12
        ";

        $monthlyTrends = $conn->executeQuery($sql)->fetchAllAssociative();
        $io->section('5. TENDANCES MENSUELLES (12 derniers mois)');
        $trendsList = [];
        foreach ($monthlyTrends as $trend) {
            $trendsList[] = "{$trend['period_key']}: {$trend['document_count']} docs, " . round($trend['avg_processing_time'], 1) . " jours (total: {$trend['total_time']} jours)";
        }
        $io->listing($trendsList);

        // 6. Vérifier les documents à risque
        $io->section('6. DOCUMENTS À RISQUE');
        $riskyDocs = $this->documentRepository->getRiskyDocumentsOptimized(7);
        $io->text("Nombre de documents à risque (>7 jours): " . count($riskyDocs));

        if (count($riskyDocs) > 0) {
            $io->text("Top 10 documents à risque:");
            $riskyList = [];
            foreach (array_slice($riskyDocs, 0, 10) as $risky) {
                $riskyList[] = "{$risky['document']->getReference()} ({$risky['state']}): {$risky['days_in_state']} jours";
            }
            $io->listing($riskyList);
        }

        // 7. Vérifier les données de forecast
        $io->section('7. DONNÉES DE FORECAST');
        $forecastStats = $this->documentRepository->getForecastDocumentStats();
        $io->listing([
            "Documents actifs: {$forecastStats['active']}",
            "Documents terminés: {$forecastStats['completed']}",
            "Total: {$forecastStats['total']}"
        ]);

        $docTypeStatsOptimized = $this->documentRepository->getDocTypeStatsOptimized();
        $io->text("Types de documents analysés: " . count($docTypeStatsOptimized));
        $optimizedList = [];
        foreach ($docTypeStatsOptimized as $type => $stats) {
            $optimizedList[] = "{$type}: {$stats['count']} docs, " . round($stats['avg_time'], 1) . " jours";
        }
        $io->listing($optimizedList);

        // 8. Test des prédictions
        $io->section('8. TEST DES PRÉDICTIONS');
        $testDocuments = $this->documentRepository->findBy([], null, 5);
        $predictionsList = [];
        foreach ($testDocuments as $doc) {
            $prediction = $this->dataAnalysisService->predictProcessingTime($doc);
            if ($prediction['avg_time']) {
                $predictionsList[] = "{$doc->getReference()} ({$doc->getDocType()}): prédiction {$prediction['avg_time']} jours (échantillon: {$prediction['sample_size']})";
            } else {
                $predictionsList[] = "{$doc->getReference()} ({$doc->getDocType()}): pas de prédiction possible";
            }
        }
        $io->listing($predictionsList);

        // 9. Analyser les problèmes potentiels
        $io->section('9. ANALYSE DES PROBLÈMES POTENTIELS');
        $problems = [];

        if ($stats['count'] < 100) {
            $problems[] = "⚠️  ÉCHANTILLON TROP PETIT: Seulement {$stats['count']} documents avec les deux visas";
        }

        if ($stats['avg_time'] < 1) {
            $problems[] = "⚠️  TEMPS MOYEN TROP COURT: {$stats['avg_time']} jours semble irréaliste";
        }

        if ($stats['min_time'] < 0) {
            $problems[] = "⚠️  TEMPS NÉGATIFS DÉTECTÉS: Problème dans les dates de visas";
        }

        if (empty($monthlyTrends)) {
            $problems[] = "⚠️  AUCUNE TENDANCE MENSUELLE: Pas de données récentes";
        }

        if (count($problems) > 0) {
            $io->listing($problems);
        } else {
            $io->success('Aucun problème majeur détecté');
        }

        $io->success('Diagnostic terminé');
        return Command::SUCCESS;
    }
}
