<?php

require_once 'vendor/autoload.php';

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env');

// C<PERSON>er le kernel Symfony
$kernel = new Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Récupérer les services nécessaires
$entityManager = $container->get('doctrine.orm.entity_manager');
$documentRepository = $entityManager->getRepository('App\Entity\Document');

echo "=== DIAGNOSTIC DES ANALYSES ET PRÉDICTIONS ===\n\n";

// 1. Vérifier le nombre total de documents
$totalDocuments = $documentRepository->count([]);
echo "1. NOMBRE TOTAL DE DOCUMENTS: {$totalDocuments}\n\n";

// 2. Analyser les documents avec visas BE_0 et Costing
$conn = $entityManager->getConnection();

$sql = "
    SELECT
        COUNT(DISTINCT d.id) as total_documents,
        COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL THEN d.id END) as with_be0_visa,
        COUNT(DISTINCT CASE WHEN v2.id IS NOT NULL THEN d.id END) as with_costing_visa,
        COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL AND v2.id IS NOT NULL THEN d.id END) as with_both_visas
    FROM document d
    LEFT JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    LEFT JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
";

$result = $conn->executeQuery($sql)->fetchAssociative();
echo "2. ANALYSE DES VISAS:\n";
echo "   - Documents avec visa BE_0: {$result['with_be0_visa']}\n";
echo "   - Documents avec visa Costing: {$result['with_costing_visa']}\n";
echo "   - Documents avec les deux visas: {$result['with_both_visas']}\n\n";

// 3. Analyser les temps de traitement calculés
$sql = "
    SELECT
        d.id,
        d.reference,
        d.doc_type,
        v1.date_visa as be0_date,
        v2.date_visa as costing_date,
        DATEDIFF(v2.date_visa, v1.date_visa) as processing_time
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE v2.date_visa >= v1.date_visa
    ORDER BY processing_time DESC
    LIMIT 20
";

$processingTimes = $conn->executeQuery($sql)->fetchAllAssociative();
echo "3. TOP 20 TEMPS DE TRAITEMENT LES PLUS LONGS:\n";
foreach ($processingTimes as $doc) {
    echo "   - {$doc['reference']} ({$doc['doc_type']}): {$doc['processing_time']} jours ({$doc['be0_date']} -> {$doc['costing_date']})\n";
}

// 4. Statistiques générales des temps de traitement
$sql = "
    SELECT
        COUNT(*) as count,
        AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time,
        MIN(DATEDIFF(v2.date_visa, v1.date_visa)) as min_time,
        MAX(DATEDIFF(v2.date_visa, v1.date_visa)) as max_time,
        STDDEV(DATEDIFF(v2.date_visa, v1.date_visa)) as std_dev
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE v2.date_visa >= v1.date_visa
    AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
";

$stats = $conn->executeQuery($sql)->fetchAssociative();
echo "\n4. STATISTIQUES GÉNÉRALES DES TEMPS DE TRAITEMENT:\n";
echo "   - Nombre d'échantillons: {$stats['count']}\n";
echo "   - Temps moyen: " . round($stats['avg_time'], 2) . " jours\n";
echo "   - Temps minimum: {$stats['min_time']} jours\n";
echo "   - Temps maximum: {$stats['max_time']} jours\n";
echo "   - Écart-type: " . round($stats['std_dev'], 2) . " jours\n\n";

// 5. Analyser par type de document
$sql = "
    SELECT
        d.doc_type,
        COUNT(*) as count,
        AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time,
        MIN(DATEDIFF(v2.date_visa, v1.date_visa)) as min_time,
        MAX(DATEDIFF(v2.date_visa, v1.date_visa)) as max_time
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE v2.date_visa >= v1.date_visa
    AND DATEDIFF(v2.date_visa, v1.date_visa) >= 0
    GROUP BY d.doc_type
    ORDER BY avg_time DESC
";

$docTypeStats = $conn->executeQuery($sql)->fetchAllAssociative();
echo "5. STATISTIQUES PAR TYPE DE DOCUMENT:\n";
foreach ($docTypeStats as $stat) {
    echo "   - {$stat['doc_type']}: {$stat['count']} docs, " . round($stat['avg_time'], 1) . " jours (min: {$stat['min_time']}, max: {$stat['max_time']})\n";
}

// 6. Analyser les tendances mensuelles
$sql = "
    SELECT
        DATE_FORMAT(v1.date_visa, '%m/%Y') as period_key,
        COUNT(*) as document_count,
        AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_processing_time,
        SUM(DATEDIFF(v2.date_visa, v1.date_visa)) as total_time
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE v2.date_visa >= v1.date_visa
    AND v1.date_visa >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(v1.date_visa, '%m/%Y')
    ORDER BY period_key DESC
    LIMIT 12
";

$monthlyTrends = $conn->executeQuery($sql)->fetchAllAssociative();
echo "\n6. TENDANCES MENSUELLES (12 derniers mois):\n";
foreach ($monthlyTrends as $trend) {
    echo "   - {$trend['period_key']}: {$trend['document_count']} docs, " . round($trend['avg_processing_time'], 1) . " jours (total: {$trend['total_time']} jours)\n";
}

// 7. Vérifier les documents à risque
echo "\n7. DOCUMENTS À RISQUE:\n";
$riskyDocs = $documentRepository->getRiskyDocumentsOptimized(7);
echo "   - Nombre de documents à risque (>7 jours): " . count($riskyDocs) . "\n";

if (count($riskyDocs) > 0) {
    echo "   - Top 10 documents à risque:\n";
    foreach (array_slice($riskyDocs, 0, 10) as $risky) {
        echo "     * {$risky['document']->getReference()} ({$risky['state']}): {$risky['days_in_state']} jours\n";
    }
}

// 8. Vérifier les prédictions (simulation manuelle)
echo "\n8. TEST DES PRÉDICTIONS (simulation):\n";
$testDocuments = $documentRepository->findBy([], null, 5);
foreach ($testDocuments as $doc) {
    // Simuler la prédiction manuellement
    $docType = $doc->getDocType();
    $sql = "
        SELECT
            COUNT(*) as count,
            AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time
        FROM document d
        INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
        INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
        WHERE d.doc_type = ? AND v2.date_visa >= v1.date_visa
    ";
    $prediction = $conn->executeQuery($sql, [$docType])->fetchAssociative();

    echo "   - {$doc->getReference()} ({$docType}): ";
    if ($prediction['count'] > 0) {
        echo "prédiction " . round($prediction['avg_time'], 1) . " jours (échantillon: {$prediction['count']})\n";
    } else {
        echo "pas de prédiction possible\n";
    }
}

// 9. Vérifier les données de forecast
echo "\n9. DONNÉES DE FORECAST:\n";
$forecastStats = $documentRepository->getForecastDocumentStats();
echo "   - Documents actifs: {$forecastStats['active']}\n";
echo "   - Documents terminés: {$forecastStats['completed']}\n";
echo "   - Total: {$forecastStats['total']}\n";

$docTypeStatsOptimized = $documentRepository->getDocTypeStatsOptimized();
echo "   - Types de documents analysés: " . count($docTypeStatsOptimized) . "\n";
foreach ($docTypeStatsOptimized as $type => $stats) {
    echo "     * {$type}: {$stats['count']} docs, " . round($stats['avg_time'], 1) . " jours\n";
}

echo "\n=== FIN DU DIAGNOSTIC ===\n";
