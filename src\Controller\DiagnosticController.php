<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use App\Service\DataAnalysisService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/diagnostic')]
class DiagnosticController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private DocumentRepository $documentRepository,
        private DataAnalysisService $dataAnalysisService
    ) {}

    #[Route('/analyses', name: 'app_diagnostic_analyses', methods: ['GET'])]
    public function analyses(): Response
    {
        $conn = $this->entityManager->getConnection();
        
        // 1. Statistiques générales
        $totalDocuments = $this->documentRepository->count([]);
        
        // 2. Analyser les visas
        $visaStats = $conn->executeQuery("
            SELECT 
                COUNT(DISTINCT d.id) as total_documents,
                COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL THEN d.id END) as with_be0_visa,
                COUNT(DISTINCT CASE WHEN v2.id IS NOT NULL THEN d.id END) as with_costing_visa,
                COUNT(DISTINCT CASE WHEN v1.id IS NOT NULL AND v2.id IS NOT NULL THEN d.id END) as with_both_visas
            FROM document d
            LEFT JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            LEFT JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
        ")->fetchAssociative();

        // 3. Analyser les temps de traitement actuels (méthode problématique)
        $currentMethodStats = $conn->executeQuery("
            SELECT 
                COUNT(*) as count,
                AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time,
                MIN(DATEDIFF(v2.date_visa, v1.date_visa)) as min_time,
                MAX(DATEDIFF(v2.date_visa, v1.date_visa)) as max_time
            FROM document d
            INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
            INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            WHERE v2.date_visa >= v1.date_visa
        ")->fetchAssociative();

        // 4. Analyser avec la nouvelle méthode
        $improvedStats = $this->dataAnalysisService->getImprovedProcessingTimeAnalysis();

        // 5. Analyser les documents avec timestamps
        $timestampStats = $conn->executeQuery("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN state_timestamps IS NOT NULL AND state_timestamps != '{}' THEN 1 END) as with_timestamps,
                COUNT(CASE WHEN current_steps IS NOT NULL AND current_steps != '{}' THEN 1 END) as with_current_steps
            FROM document
        ")->fetchAssociative();

        // 6. Identifier les problèmes
        $problems = [];
        
        if ($currentMethodStats['count'] < 100) {
            $problems[] = [
                'severity' => 'critical',
                'message' => "Échantillon trop petit avec la méthode actuelle: {$currentMethodStats['count']} documents",
                'impact' => 'Les prédictions sont basées sur trop peu de données'
            ];
        }
        
        if ($currentMethodStats['avg_time'] < 5) {
            $problems[] = [
                'severity' => 'critical',
                'message' => "Temps moyen irréaliste: " . round($currentMethodStats['avg_time'], 1) . " jours",
                'impact' => 'Les prédictions sous-estiment drastiquement les délais réels'
            ];
        }
        
        $coverageRate = ($visaStats['with_both_visas'] / $totalDocuments) * 100;
        if ($coverageRate < 20) {
            $problems[] = [
                'severity' => 'warning',
                'message' => "Faible couverture des données: " . round($coverageRate, 1) . "% des documents ont les deux visas requis",
                'impact' => 'La majorité des documents ne sont pas pris en compte dans les analyses'
            ];
        }

        return $this->render('diagnostic/analyses.html.twig', [
            'total_documents' => $totalDocuments,
            'visa_stats' => $visaStats,
            'current_method_stats' => $currentMethodStats,
            'improved_stats' => $improvedStats,
            'timestamp_stats' => $timestampStats,
            'problems' => $problems,
            'coverage_rate' => $coverageRate
        ]);
    }

    #[Route('/api/quick-check', name: 'app_diagnostic_api_quick_check', methods: ['GET'])]
    public function quickCheck(): JsonResponse
    {
        $conn = $this->entityManager->getConnection();
        
        // Vérifications rapides
        $checks = [
            'total_documents' => $this->documentRepository->count([]),
            'documents_with_timestamps' => $conn->executeQuery("
                SELECT COUNT(*) FROM document 
                WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}'
            ")->fetchOne(),
            'documents_with_both_visas' => $conn->executeQuery("
                SELECT COUNT(DISTINCT d.id) FROM document d
                INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
                INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
            ")->fetchOne(),
            'avg_time_current_method' => $conn->executeQuery("
                SELECT AVG(DATEDIFF(v2.date_visa, v1.date_visa)) FROM document d
                INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
                INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
                WHERE v2.date_visa >= v1.date_visa
            ")->fetchOne()
        ];
        
        // Évaluer la santé du système
        $health = 'good';
        $issues = [];
        
        if ($checks['documents_with_both_visas'] < 100) {
            $health = 'poor';
            $issues[] = 'Échantillon de données insuffisant';
        }
        
        if ($checks['avg_time_current_method'] < 5) {
            $health = 'poor';
            $issues[] = 'Temps moyens irréalistes';
        }
        
        $coverage = ($checks['documents_with_both_visas'] / $checks['total_documents']) * 100;
        if ($coverage < 20) {
            $health = $health === 'good' ? 'warning' : $health;
            $issues[] = 'Faible couverture des données';
        }
        
        return new JsonResponse([
            'health' => $health,
            'checks' => $checks,
            'coverage_percentage' => round($coverage, 1),
            'issues' => $issues,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    #[Route('/fix-recommendations', name: 'app_diagnostic_fix_recommendations', methods: ['GET'])]
    public function fixRecommendations(): Response
    {
        $recommendations = [
            [
                'priority' => 'high',
                'title' => 'Corriger la méthode de calcul des temps de traitement',
                'description' => 'Utiliser les timestamps d\'état complets au lieu de seulement BE_0 → Costing',
                'implementation' => 'Implémenter getCompleteProcessingTimeStats() dans DocumentRepository',
                'impact' => 'Temps de traitement plus réalistes et prédictions plus précises'
            ],
            [
                'priority' => 'high',
                'title' => 'Élargir les critères de documents "terminés"',
                'description' => 'Ne pas se limiter aux documents avec visa Costing',
                'implementation' => 'Définir des critères de fin de workflow par type de document',
                'impact' => 'Échantillon de données plus large et représentatif'
            ],
            [
                'priority' => 'medium',
                'title' => 'Améliorer l\'algorithme de prédiction',
                'description' => 'Remplacer la régression linéaire simple par un modèle plus sophistiqué',
                'implementation' => 'Intégrer des facteurs saisonniers et de type de document',
                'impact' => 'Prédictions plus précises et fiables'
            ],
            [
                'priority' => 'medium',
                'title' => 'Implémenter la validation des données',
                'description' => 'Ajouter des contrôles de cohérence sur les timestamps',
                'implementation' => 'Créer des règles de validation et des alertes',
                'impact' => 'Détection précoce des problèmes de données'
            ],
            [
                'priority' => 'low',
                'title' => 'Ajouter des métriques de qualité',
                'description' => 'Suivre la précision des prédictions dans le temps',
                'implementation' => 'Comparer prédictions vs réalité et ajuster les modèles',
                'impact' => 'Amélioration continue de la précision'
            ]
        ];

        return $this->render('diagnostic/recommendations.html.twig', [
            'recommendations' => $recommendations
        ]);
    }
}
