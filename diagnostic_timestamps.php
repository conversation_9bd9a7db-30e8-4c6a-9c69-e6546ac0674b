<?php

require_once 'vendor/autoload.php';

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env');

// C<PERSON>er le kernel Symfony
$kernel = new Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Récupérer les services nécessaires
$entityManager = $container->get('doctrine.orm.entity_manager');
$conn = $entityManager->getConnection();

echo "=== DIAGNOSTIC DES TIMESTAMPS D'ÉTAT ===\n\n";

// 1. Analyser la structure des timestamps
echo "1. ANALYSE DE LA STRUCTURE DES TIMESTAMPS:\n";
$sql = "
    SELECT 
        d.id,
        d.reference,
        d.state_timestamps
    FROM document d
    WHERE d.state_timestamps IS NOT NULL 
    AND d.state_timestamps != '{}'
    AND d.state_timestamps != ''
    LIMIT 10
";

$result = $conn->executeQuery($sql);
$sampleDocs = $result->fetchAllAssociative();

foreach ($sampleDocs as $doc) {
    $timestamps = json_decode($doc['state_timestamps'], true);
    echo "   Document {$doc['reference']}:\n";
    
    if (is_array($timestamps)) {
        foreach ($timestamps as $state => $entries) {
            if (is_array($entries)) {
                $entryCount = count($entries);
                $hasExit = false;
                foreach ($entries as $entry) {
                    if (isset($entry['exit']) && $entry['exit'] !== null) {
                        $hasExit = true;
                        break;
                    }
                }
                echo "     - {$state}: {$entryCount} entrée(s), " . ($hasExit ? "avec sortie" : "sans sortie") . "\n";
            }
        }
    }
    echo "\n";
}

// 2. Compter les états avec/sans timestamps de sortie
echo "2. STATISTIQUES DES TIMESTAMPS DE SORTIE:\n";
$sql = "
    SELECT 
        d.state_timestamps
    FROM document d
    WHERE d.state_timestamps IS NOT NULL 
    AND d.state_timestamps != '{}'
    AND d.state_timestamps != ''
    LIMIT 1000
";

$result = $conn->executeQuery($sql);
$documents = $result->fetchAllAssociative();

$stateStats = [];
$totalDocs = count($documents);

foreach ($documents as $docData) {
    $timestamps = json_decode($docData['state_timestamps'], true);
    
    if (!is_array($timestamps)) {
        continue;
    }
    
    foreach ($timestamps as $state => $entries) {
        if (!isset($stateStats[$state])) {
            $stateStats[$state] = [
                'total_entries' => 0,
                'with_exit' => 0,
                'without_exit' => 0
            ];
        }
        
        if (is_array($entries)) {
            foreach ($entries as $entry) {
                $stateStats[$state]['total_entries']++;
                if (isset($entry['exit']) && $entry['exit'] !== null) {
                    $stateStats[$state]['with_exit']++;
                } else {
                    $stateStats[$state]['without_exit']++;
                }
            }
        }
    }
}

echo "   États trouvés dans les timestamps:\n";
foreach ($stateStats as $state => $stats) {
    $exitRate = $stats['total_entries'] > 0 ? round(($stats['with_exit'] / $stats['total_entries']) * 100, 1) : 0;
    echo "   - {$state}: {$stats['total_entries']} entrées, {$stats['with_exit']} avec sortie ({$exitRate}%)\n";
}

// 3. Analyser les current_steps vs state_timestamps
echo "\n3. COMPARAISON CURRENT_STEPS vs STATE_TIMESTAMPS:\n";
$sql = "
    SELECT 
        d.current_steps,
        d.state_timestamps
    FROM document d
    WHERE d.current_steps IS NOT NULL 
    AND d.current_steps != '{}'
    AND d.state_timestamps IS NOT NULL 
    AND d.state_timestamps != '{}'
    LIMIT 100
";

$result = $conn->executeQuery($sql);
$documents = $result->fetchAllAssociative();

$currentStepsStates = [];
$timestampStates = [];

foreach ($documents as $docData) {
    $currentSteps = json_decode($docData['current_steps'], true);
    $timestamps = json_decode($docData['state_timestamps'], true);
    
    if (is_array($currentSteps)) {
        foreach (array_keys($currentSteps) as $state) {
            $currentStepsStates[$state] = ($currentStepsStates[$state] ?? 0) + 1;
        }
    }
    
    if (is_array($timestamps)) {
        foreach (array_keys($timestamps) as $state) {
            $timestampStates[$state] = ($timestampStates[$state] ?? 0) + 1;
        }
    }
}

echo "   États dans current_steps:\n";
arsort($currentStepsStates);
foreach (array_slice($currentStepsStates, 0, 10, true) as $state => $count) {
    echo "   - {$state}: {$count} documents\n";
}

echo "\n   États dans state_timestamps:\n";
arsort($timestampStates);
foreach (array_slice($timestampStates, 0, 10, true) as $state => $count) {
    echo "   - {$state}: {$count} documents\n";
}

// 4. Identifier les états manquants
echo "\n4. ÉTATS PRÉSENTS DANS CURRENT_STEPS MAIS ABSENTS DES TIMESTAMPS:\n";
$missingInTimestamps = array_diff(array_keys($currentStepsStates), array_keys($timestampStates));
if (count($missingInTimestamps) > 0) {
    foreach ($missingInTimestamps as $state) {
        echo "   - {$state}: présent dans {$currentStepsStates[$state]} current_steps mais absent des timestamps\n";
    }
} else {
    echo "   - Tous les états de current_steps sont présents dans les timestamps\n";
}

// 5. Analyser pourquoi Project avait 588.8 jours
echo "\n5. ANALYSE DE L'ANCIEN PROBLÈME 'Project' (588.8 jours):\n";

// Simuler l'ancienne méthode pour Project
$sql = "
    SELECT 
        d.id,
        d.reference,
        DATEDIFF(v2.date_visa, v1.date_visa) as processing_time
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE JSON_EXTRACT(d.current_steps, '$.\"Project\"') IS NOT NULL
    AND v2.date_visa >= v1.date_visa
    ORDER BY processing_time DESC
    LIMIT 10
";

$result = $conn->executeQuery($sql);
$oldProjectDocs = $result->fetchAllAssociative();

if (count($oldProjectDocs) > 0) {
    echo "   Documents 'Project' avec ancienne méthode (BE_0 → Costing):\n";
    $totalTime = 0;
    foreach ($oldProjectDocs as $doc) {
        echo "     - {$doc['reference']}: {$doc['processing_time']} jours\n";
        $totalTime += $doc['processing_time'];
    }
    $avgTime = round($totalTime / count($oldProjectDocs), 1);
    echo "   Temps moyen avec ancienne méthode: {$avgTime} jours\n";
    echo "   PROBLÈME: Cette méthode attribue TOUT le temps BE_0→Costing à CHAQUE état présent!\n";
} else {
    echo "   - Aucun document 'Project' trouvé avec l'ancienne méthode\n";
}

// 6. Recommandations
echo "\n6. RECOMMANDATIONS POUR AMÉLIORER LES DONNÉES:\n";
$recommendations = [];

$statesWithLowExitRate = array_filter($stateStats, function($stats) {
    return $stats['total_entries'] > 10 && ($stats['with_exit'] / $stats['total_entries']) < 0.5;
});

if (count($statesWithLowExitRate) > 0) {
    $recommendations[] = "Améliorer l'enregistrement des timestamps de sortie pour " . count($statesWithLowExitRate) . " états";
}

if (count($missingInTimestamps) > 0) {
    $recommendations[] = "Ajouter des timestamps pour " . count($missingInTimestamps) . " états présents dans current_steps";
}

if (count($stateStats) < 5) {
    $recommendations[] = "Élargir la couverture des timestamps (seulement " . count($stateStats) . " états trackés)";
}

if (count($recommendations) > 0) {
    foreach ($recommendations as $i => $rec) {
        echo "   " . ($i + 1) . ". {$rec}\n";
    }
} else {
    echo "   - Système de timestamps fonctionnel\n";
}

echo "\n=== FIN DU DIAGNOSTIC ===\n";
