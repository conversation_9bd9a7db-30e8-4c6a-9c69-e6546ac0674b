{% extends 'base.html.twig' %}

{% block title %}Recommandations de Correction{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Plan de Correction des Analyses et Prédictions
                    </h1>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Voici un plan détaillé pour corriger les problèmes identifiés dans le système d'analyses.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recommandations par priorité -->
    {% set priorities = {'high': 'Priorité Haute', 'medium': 'Priorité Moyenne', 'low': 'Priorité Basse'} %}
    {% set priority_colors = {'high': 'danger', 'medium': 'warning', 'low': 'info'} %}
    {% set priority_icons = {'high': 'exclamation-circle', 'medium': 'exclamation-triangle', 'low': 'info-circle'} %}

    {% for priority, priority_label in priorities %}
        {% set priority_recommendations = recommendations|filter(r => r.priority == priority) %}
        {% if priority_recommendations|length > 0 %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-{{ priority_colors[priority] }}">
                    <div class="card-header bg-{{ priority_colors[priority] }} text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-{{ priority_icons[priority] }} me-2"></i>
                            {{ priority_label }}
                        </h4>
                    </div>
                    <div class="card-body">
                        {% for recommendation in priority_recommendations %}
                        <div class="card mb-3 border-left-{{ priority_colors[priority] }}">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5 class="card-title text-{{ priority_colors[priority] }}">
                                            {{ recommendation.title }}
                                        </h5>
                                        <p class="card-text">{{ recommendation.description }}</p>
                                        
                                        <div class="mt-3">
                                            <h6 class="text-muted">
                                                <i class="fas fa-code me-1"></i>
                                                Implémentation:
                                            </h6>
                                            <p class="small bg-light p-2 rounded">{{ recommendation.implementation }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="bg-light p-3 rounded h-100">
                                            <h6 class="text-success">
                                                <i class="fas fa-bullseye me-1"></i>
                                                Impact Attendu:
                                            </h6>
                                            <p class="small mb-0">{{ recommendation.impact }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    {% endfor %}

    <!-- Résumé des améliorations -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Résumé des Améliorations Attendues
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-danger">Situation Actuelle</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Documents analysés</span>
                                    <span class="badge bg-danger">~50-100</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Temps moyen</span>
                                    <span class="badge bg-danger">2-5 jours</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Couverture</span>
                                    <span class="badge bg-danger">< 20%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Fiabilité prédictions</span>
                                    <span class="badge bg-danger">Faible</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-success">Après Corrections</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Documents analysés</span>
                                    <span class="badge bg-success">500-1000+</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Temps moyen</span>
                                    <span class="badge bg-success">15-30 jours</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Couverture</span>
                                    <span class="badge bg-success">60-80%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Fiabilité prédictions</span>
                                    <span class="badge bg-success">Élevée</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan d'implémentation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Plan d'Implémentation Suggéré
                    </h4>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="text-danger">Phase 1 - Corrections Critiques (Semaine 1)</h6>
                                <ul>
                                    <li>Implémenter getCompleteProcessingTimeStats()</li>
                                    <li>Corriger la méthode de calcul des temps de traitement</li>
                                    <li>Tester avec un échantillon de documents</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="text-warning">Phase 2 - Améliorations (Semaine 2-3)</h6>
                                <ul>
                                    <li>Élargir les critères de documents terminés</li>
                                    <li>Améliorer l'algorithme de prédiction</li>
                                    <li>Ajouter la validation des données</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="text-info">Phase 3 - Optimisations (Semaine 4)</h6>
                                <ul>
                                    <li>Implémenter les métriques de qualité</li>
                                    <li>Ajouter le monitoring continu</li>
                                    <li>Optimiser les performances</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-play me-2"></i>
                        Prochaines Étapes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ path('app_diagnostic_analyses') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Retour au Diagnostic
                        </a>
                        <button class="btn btn-success" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            Imprimer le Plan
                        </button>
                        <a href="{{ path('app_forecast') }}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-1"></i>
                            Voir les Prévisions Actuelles
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #dee2e6;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-left-info {
    border-left: 4px solid #0dcaf0 !important;
}
</style>
{% endblock %}
