<?php

require_once 'vendor/autoload.php';

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env');

// Créer le kernel Symfony
$kernel = new Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Récupérer les services nécessaires
$entityManager = $container->get('doctrine.orm.entity_manager');
$documentRepository = $entityManager->getRepository('App\Entity\Document');

echo "=== TEST DES CORRECTIONS DES GOULOTS D'ÉTRANGLEMENT ===\n\n";

// 1. Tester la nouvelle méthode getAverageTimeByState
echo "1. TEST getAverageTimeByState() (sans valeurs hardcodées):\n";
$averageTimes = $documentRepository->getAverageTimeByState();
echo "   Nombre d'états avec données calculées: " . count($averageTimes) . "\n";
foreach ($averageTimes as $state => $avgTime) {
    echo "   - {$state}: {$avgTime} jours\n";
}

// 2. Tester la nouvelle méthode getStateBottleneckAnalysis
echo "\n2. TEST getStateBottleneckAnalysis() (méthode corrigée):\n";
$bottleneckAnalysis = $documentRepository->getStateBottleneckAnalysis();
echo "   Nombre d'états analysés: " . count($bottleneckAnalysis) . "\n";
echo "   Top 10 goulots d'étranglement:\n";
$count = 0;
foreach ($bottleneckAnalysis as $state => $stats) {
    if ($count >= 10) break;
    echo "   - {$state}: {$stats['avg_time']} jours (docs: {$stats['document_count']}, total: {$stats['total_time']})\n";
    $count++;
}

// 3. Tester spécifiquement l'état "Project"
echo "\n3. TEST spécifique pour l'état 'Project':\n";
if (isset($bottleneckAnalysis['Project'])) {
    $projectStats = $bottleneckAnalysis['Project'];
    echo "   - Temps moyen: {$projectStats['avg_time']} jours\n";
    echo "   - Nombre de documents: {$projectStats['document_count']}\n";
    echo "   - Temps total: {$projectStats['total_time']} jours\n";
    echo "   - Min: {$projectStats['min_time']} jours\n";
    echo "   - Max: {$projectStats['max_time']} jours\n";
} else {
    echo "   - État 'Project' non trouvé dans l'analyse\n";
}

// 4. Tester getProcessingTimesForState pour Project
echo "\n4. TEST getProcessingTimesForState('Project'):\n";
$projectTimes = $documentRepository->getProcessingTimesForState('Project');
echo "   Nombre d'échantillons: " . count($projectTimes) . "\n";
if (count($projectTimes) > 0) {
    echo "   Temps individuels (premiers 10): " . implode(', ', array_slice($projectTimes, 0, 10)) . "\n";
    echo "   Moyenne calculée: " . round(array_sum($projectTimes) / count($projectTimes), 1) . " jours\n";
    echo "   Min: " . min($projectTimes) . " jours\n";
    echo "   Max: " . max($projectTimes) . " jours\n";
}

// 5. Comparer avec l'ancienne méthode (simulation)
echo "\n5. COMPARAISON avec l'ancienne méthode (simulation):\n";
$conn = $entityManager->getConnection();

// Ancienne méthode (problématique)
$oldMethodSql = "
    SELECT 
        COUNT(*) as count,
        AVG(DATEDIFF(v2.date_visa, v1.date_visa)) as avg_time
    FROM document d
    INNER JOIN visa v1 ON v1.released_drawing_id = d.id AND v1.name = 'visa_BE_0' AND v1.status = 'valid'
    INNER JOIN visa v2 ON v2.released_drawing_id = d.id AND v2.name = 'visa_Costing' AND v2.status = 'valid'
    WHERE JSON_EXTRACT(d.current_steps, '$.\"Project\"') IS NOT NULL
    AND v2.date_visa >= v1.date_visa
";

$oldResult = $conn->executeQuery($oldMethodSql)->fetchAssociative();
echo "   Ancienne méthode (BE_0 → Costing):\n";
echo "     - Échantillon: {$oldResult['count']} documents\n";
echo "     - Temps moyen: " . round($oldResult['avg_time'], 1) . " jours\n";

echo "   Nouvelle méthode (timestamps réels):\n";
if (isset($bottleneckAnalysis['Project'])) {
    echo "     - Échantillon: {$bottleneckAnalysis['Project']['document_count']} documents\n";
    echo "     - Temps moyen: {$bottleneckAnalysis['Project']['avg_time']} jours\n";
} else {
    echo "     - Pas de données disponibles\n";
}

// 6. Analyser la qualité des données
echo "\n6. ANALYSE DE LA QUALITÉ DES DONNÉES:\n";
$totalDocs = $documentRepository->count([]);
$docsWithTimestamps = $conn->executeQuery("
    SELECT COUNT(*) FROM document 
    WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''
")->fetchOne();

echo "   - Documents totaux: {$totalDocs}\n";
echo "   - Documents avec timestamps: {$docsWithTimestamps}\n";
echo "   - Couverture: " . round(($docsWithTimestamps / $totalDocs) * 100, 1) . "%\n";

// 7. Vérifier les états les plus problématiques
echo "\n7. ÉTATS LES PLUS PROBLÉMATIQUES (temps moyen > 10 jours):\n";
$problematicStates = array_filter($bottleneckAnalysis, function($stats) {
    return $stats['avg_time'] > 10;
});

if (count($problematicStates) > 0) {
    foreach ($problematicStates as $state => $stats) {
        echo "   - {$state}: {$stats['avg_time']} jours (échantillon: {$stats['document_count']})\n";
    }
} else {
    echo "   - Aucun état avec temps moyen > 10 jours\n";
}

// 8. Recommandations
echo "\n8. RECOMMANDATIONS:\n";
$recommendations = [];

if ($docsWithTimestamps < $totalDocs * 0.5) {
    $recommendations[] = "Améliorer la couverture des timestamps (actuellement " . round(($docsWithTimestamps / $totalDocs) * 100, 1) . "%)";
}

if (count($averageTimes) < 5) {
    $recommendations[] = "Peu d'états ont des données calculées (" . count($averageTimes) . " états)";
}

$highVarianceStates = array_filter($bottleneckAnalysis, function($stats) {
    return $stats['max_time'] > $stats['min_time'] * 5 && $stats['document_count'] > 5;
});

if (count($highVarianceStates) > 0) {
    $recommendations[] = "Analyser la variabilité élevée dans " . count($highVarianceStates) . " états";
}

if (count($recommendations) > 0) {
    foreach ($recommendations as $i => $rec) {
        echo "   " . ($i + 1) . ". {$rec}\n";
    }
} else {
    echo "   - Système fonctionnel, pas de recommandations majeures\n";
}

echo "\n=== FIN DU TEST ===\n";
