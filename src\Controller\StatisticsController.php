<?php

namespace App\Controller;

use App\Entity\Document;
use App\Repository\DocumentRepository;
use App\Service\DataAnalysisService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/statistics')]
class StatisticsController extends AbstractController
{
    private array $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
    private array $procTypes = ['E', 'F', 'F30', 'F40', 'F50'];
    private array $materialTypes = ['HALB', 'FERT', 'ROH', 'NLAG', 'DIEN', 'HAWA'];
    #[Route('/', name: 'app_statistics', methods: ['GET'])]
    public function index(DocumentRepository $documentRepository, DataAnalysisService $dataAnalysisService): Response
    {
        $documents = $documentRepository->findAll();

        // Séparer les documents actifs et terminés pour des analyses différenciées
        $activeDocuments = [];
        $completedDocuments = [];

        foreach ($documents as $document) {
            if ($dataAnalysisService->isDocumentCompleted($document)) {
                $completedDocuments[] = $document;
            } else {
                $activeDocuments[] = $document;
            }
        }

        // Statistiques générales
        $totalDocuments = count($documents);
        $totalActiveDocuments = count($activeDocuments);
        $totalCompletedDocuments = count($completedDocuments);

        // Analyses basées sur les documents actifs
        $stateDistribution = $this->getStateDistribution($activeDocuments);
        $documentAgeDistribution = $this->getDocumentAgeDistribution($activeDocuments);

        // Analyses basées sur les données réelles du repository
        $averageTimeByState = $documentRepository->getAverageTimeByState();
        $bottlenecks = $this->getBottlenecks($averageTimeByState);
        $returnRates = $this->getReturnRates($completedDocuments);

        return $this->render('statistics/index.html.twig', [
            'totalDocuments' => $totalDocuments,
            'totalActiveDocuments' => $totalActiveDocuments,
            'totalCompletedDocuments' => $totalCompletedDocuments,
            'stateDistribution' => $stateDistribution,
            'averageTimeByState' => $averageTimeByState,
            'bottlenecks' => $bottlenecks,
            'returnRates' => $returnRates,
            'documentAgeDistribution' => $documentAgeDistribution,
        ]);
    }

    /**
     * Calcule la répartition des documents par état
     */
    private function getStateDistribution(array $documents): array
    {
        $distribution = [];

        foreach ($documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            if (!$currentSteps) {
                continue;
            }

            foreach (array_keys($currentSteps) as $state) {
                if (!isset($distribution[$state])) {
                    $distribution[$state] = 0;
                }
                $distribution[$state]++;
            }
        }

        // Trier par nombre de documents décroissant
        arsort($distribution);

        return $distribution;
    }

    // Méthode supprimée - utilise maintenant DocumentRepository::getAverageTimeByState()

    /**
     * Identifie les goulots d'étranglement (états avec les temps moyens les plus longs)
     */
    private function getBottlenecks(array $averageTimeByState): array
    {
        // Prendre les 5 états avec les temps moyens les plus longs
        return array_slice($averageTimeByState, 0, 5, true);
    }

    /**
     * Calcule les taux de retour pour chaque état
     */
    private function getReturnRates(array $documents): array
    {
        $returnCounts = [];
        $stateCounts = [];

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            foreach ($rawTimestamps as $state => $entries) {
                if (!isset($stateCounts[$state])) {
                    $stateCounts[$state] = 0;
                    $returnCounts[$state] = 0;
                }

                $stateCounts[$state]++;

                // Si un état a plus d'une entrée, cela signifie qu'il y a eu un retour
                if (is_array($entries) && count($entries) > 1) {
                    $returnCounts[$state]++;
                }
            }
        }

        // Calculer les taux de retour
        $returnRates = [];
        foreach ($stateCounts as $state => $count) {
            if ($count > 0) {
                $returnRates[$state] = round(($returnCounts[$state] / $count) * 100, 1);
            } else {
                $returnRates[$state] = 0;
            }
        }

        // Trier par taux de retour décroissant
        arsort($returnRates);

        return $returnRates;
    }

    /**
     * Calcule la répartition des documents par âge
     */
    private function getDocumentAgeDistribution(array $documents): array
    {
        $distribution = [
            'less_than_week' => 0,
            'one_to_two_weeks' => 0,
            'two_to_four_weeks' => 0,
            'more_than_month' => 0,
        ];

        $now = new \DateTime();

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            // Trouver la date d'entrée la plus ancienne
            $oldestDate = null;
            foreach ($rawTimestamps as $state => $entries) {
                if (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($oldestDate === null || $date < $oldestDate) {
                        $oldestDate = $date;
                    }
                } elseif (is_array($entries) && !empty($entries)) {
                    // Nouveau format
                    $firstEntry = reset($entries);
                    $date = new \DateTime($firstEntry['enter']);
                    if ($oldestDate === null || $date < $oldestDate) {
                        $oldestDate = $date;
                    }
                }
            }

            if ($oldestDate) {
                $diff = $now->diff($oldestDate);
                $days = $diff->days;

                if ($days < 7) {
                    $distribution['less_than_week']++;
                } elseif ($days < 14) {
                    $distribution['one_to_two_weeks']++;
                } elseif ($days < 28) {
                    $distribution['two_to_four_weeks']++;
                } else {
                    $distribution['more_than_month']++;
                }
            }
        }

        return $distribution;
    }

    #[Route('/document-types', name: 'app_statistics_document_types', methods: ['GET'])]
    public function documentTypes(DocumentRepository $documentRepository, DataAnalysisService $dataAnalysisService): Response
    {
        $documents = $documentRepository->findAll();

        // Séparer les documents actifs et terminés pour des analyses différenciées
        $activeDocuments = [];
        $completedDocuments = [];

        foreach ($documents as $document) {
            if ($dataAnalysisService->isDocumentCompleted($document)) {
                $completedDocuments[] = $document;
            } else {
                $activeDocuments[] = $document;
            }
        }

        // Statistiques par type de document
        $docTypeStats = $this->getDocTypeStats($documents); // Tous les documents pour les comptages
        $procTypeStats = $this->getProcTypeStats($documents); // Tous les documents pour les comptages
        $materialTypeStats = $this->getMaterialTypeStats($documents); // Tous les documents pour les comptages
        $timeByDocType = $this->getTimeByDocType($completedDocuments); // Seulement les documents terminés pour les temps
        $statesByDocType = $this->getStatesByDocType($activeDocuments); // Seulement les documents actifs pour les états

        return $this->render('statistics/document_types.html.twig', [
            'docTypeStats' => $docTypeStats,
            'procTypeStats' => $procTypeStats,
            'materialTypeStats' => $materialTypeStats,
            'timeByDocType' => $timeByDocType,
            'statesByDocType' => $statesByDocType,
            'totalActiveDocuments' => count($activeDocuments),
            'totalCompletedDocuments' => count($completedDocuments),
        ]);
    }

    /**
     * Calcule les statistiques par type de document
     */
    private function getDocTypeStats(array $documents): array
    {
        $stats = [];

        // Initialiser les statistiques pour chaque type de document
        foreach ($this->docTypes as $docType) {
            $stats[$docType] = [
                'count' => 0,
                'percentage' => 0,
                'avg_time' => 0,
                'total_time' => 0,
            ];
        }

        $totalDocs = count($documents);
        $docTypeCounts = [];
        $docTypeTotalTime = [];

        foreach ($documents as $document) {
            $docType = $document->getDocType();
            if (!$docType || !in_array($docType, $this->docTypes)) {
                continue;
            }

            if (!isset($docTypeCounts[$docType])) {
                $docTypeCounts[$docType] = 0;
                $docTypeTotalTime[$docType] = 0;
            }

            $docTypeCounts[$docType]++;

            // Calculer le temps total depuis la création
            $rawTimestamps = $document->getRawStateTimestamps();
            if ($rawTimestamps) {
                $totalDays = 0;
                foreach ($rawTimestamps as $state => $entries) {
                    $stateDays = $document->getTotalDaysInState($state);
                    if ($stateDays !== null) {
                        $totalDays += $stateDays;
                    }
                }
                $docTypeTotalTime[$docType] += $totalDays;
            }
        }

        // Calculer les statistiques finales
        foreach ($this->docTypes as $docType) {
            if (isset($docTypeCounts[$docType]) && $docTypeCounts[$docType] > 0) {
                $stats[$docType]['count'] = $docTypeCounts[$docType];
                $stats[$docType]['percentage'] = round(($docTypeCounts[$docType] / $totalDocs) * 100, 1);
                $stats[$docType]['total_time'] = $docTypeTotalTime[$docType];
                $stats[$docType]['avg_time'] = round($docTypeTotalTime[$docType] / $docTypeCounts[$docType], 1);
            }
        }

        return $stats;
    }

    /**
     * Calcule les statistiques par type de processus
     */
    private function getProcTypeStats(array $documents): array
    {
        $stats = [];

        // Initialiser les statistiques pour chaque type de processus
        foreach ($this->procTypes as $procType) {
            $stats[$procType] = [
                'count' => 0,
                'percentage' => 0,
            ];
        }

        $totalDocs = count($documents);
        $procTypeCounts = [];

        foreach ($documents as $document) {
            $procType = $document->getProcType();
            if (!$procType || !in_array($procType, $this->procTypes)) {
                continue;
            }

            if (!isset($procTypeCounts[$procType])) {
                $procTypeCounts[$procType] = 0;
            }

            $procTypeCounts[$procType]++;
        }

        // Calculer les statistiques finales
        foreach ($this->procTypes as $procType) {
            if (isset($procTypeCounts[$procType])) {
                $stats[$procType]['count'] = $procTypeCounts[$procType];
                $stats[$procType]['percentage'] = round(($procTypeCounts[$procType] / $totalDocs) * 100, 1);
            }
        }

        return $stats;
    }

    /**
     * Calcule les statistiques par type de matériau
     */
    private function getMaterialTypeStats(array $documents): array
    {
        $stats = [];

        // Initialiser les statistiques pour chaque type de matériau
        foreach ($this->materialTypes as $materialType) {
            $stats[$materialType] = [
                'count' => 0,
                'percentage' => 0,
            ];
        }

        $totalDocs = count($documents);
        $materialTypeCounts = [];

        foreach ($documents as $document) {
            $matProdType = $document->getMatProdType();
            if (!$matProdType || !in_array($matProdType, $this->materialTypes)) {
                continue;
            }

            if (!isset($materialTypeCounts[$matProdType])) {
                $materialTypeCounts[$matProdType] = 0;
            }

            $materialTypeCounts[$matProdType]++;
        }

        // Calculer les statistiques finales
        foreach ($this->materialTypes as $materialType) {
            if (isset($materialTypeCounts[$materialType])) {
                $stats[$materialType]['count'] = $materialTypeCounts[$materialType];
                $stats[$materialType]['percentage'] = round(($materialTypeCounts[$materialType] / $totalDocs) * 100, 1);
            }
        }

        return $stats;
    }

    /**
     * Calcule le temps moyen passé dans chaque état par type de document
     */
    private function getTimeByDocType(array $documents): array
    {
        $timeByDocType = [];

        // Initialiser les statistiques pour chaque type de document
        foreach ($this->docTypes as $docType) {
            $timeByDocType[$docType] = [];
        }

        foreach ($documents as $document) {
            $docType = $document->getDocType();
            if (!$docType || !in_array($docType, $this->docTypes)) {
                continue;
            }

            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            foreach ($rawTimestamps as $state => $entries) {
                $totalDays = $document->getTotalDaysInState($state);

                if ($totalDays === null) {
                    continue;
                }

                if (!isset($timeByDocType[$docType][$state])) {
                    $timeByDocType[$docType][$state] = [
                        'total_days' => 0,
                        'count' => 0,
                        'avg_days' => 0,
                    ];
                }

                $timeByDocType[$docType][$state]['total_days'] += $totalDays;
                $timeByDocType[$docType][$state]['count']++;
            }
        }

        // Calculer les moyennes
        foreach ($this->docTypes as $docType) {
            foreach ($timeByDocType[$docType] as $state => $data) {
                if ($data['count'] > 0) {
                    $timeByDocType[$docType][$state]['avg_days'] = round($data['total_days'] / $data['count'], 1);
                }
            }

            // Trier par temps moyen décroissant
            uasort($timeByDocType[$docType], function($a, $b) {
                return $b['avg_days'] <=> $a['avg_days'];
            });
        }

        return $timeByDocType;
    }

    /**
     * Calcule la répartition des états par type de document
     */
    private function getStatesByDocType(array $documents): array
    {
        $statesByDocType = [];

        // Initialiser les statistiques pour chaque type de document
        foreach ($this->docTypes as $docType) {
            $statesByDocType[$docType] = [];
        }

        foreach ($documents as $document) {
            $docType = $document->getDocType();
            if (!$docType || !in_array($docType, $this->docTypes)) {
                continue;
            }

            $currentSteps = $document->getCurrentSteps();
            if (!$currentSteps) {
                continue;
            }

            foreach (array_keys($currentSteps) as $state) {
                if (!isset($statesByDocType[$docType][$state])) {
                    $statesByDocType[$docType][$state] = 0;
                }

                $statesByDocType[$docType][$state]++;
            }
        }

        // Trier par nombre de documents décroissant
        foreach ($this->docTypes as $docType) {
            arsort($statesByDocType[$docType]);
        }

        return $statesByDocType;
    }

    #[Route('/workflow-analysis', name: 'app_statistics_workflow', methods: ['GET'])]
    public function workflowAnalysis(DocumentRepository $documentRepository, DataAnalysisService $dataAnalysisService): Response
    {
        $documents = $documentRepository->findAll();

        // Séparer les documents actifs et terminés pour des analyses différenciées
        $activeDocuments = [];
        $completedDocuments = [];

        foreach ($documents as $document) {
            if ($dataAnalysisService->isDocumentCompleted($document)) {
                $completedDocuments[] = $document;
            } else {
                $activeDocuments[] = $document;
            }
        }

        // Analyse du workflow - utiliser les documents terminés pour les analyses de performance
        $transitionCounts = $this->getTransitionCounts($completedDocuments);
        $averageTransitionsPerDocument = $this->getAverageTransitionsPerDocument($completedDocuments);
        $workflowEfficiency = $this->getWorkflowEfficiency($completedDocuments);

        return $this->render('statistics/workflow.html.twig', [
            'transitionCounts' => $transitionCounts,
            'averageTransitionsPerDocument' => $averageTransitionsPerDocument,
            'workflowEfficiency' => $workflowEfficiency,
            'totalActiveDocuments' => count($activeDocuments),
            'totalCompletedDocuments' => count($completedDocuments),
        ]);
    }

    /**
     * Compte le nombre de transitions entre les états
     */
    private function getTransitionCounts(array $documents): array
    {
        $transitions = [];

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps || !is_array($rawTimestamps)) {
                continue;
            }

            // Créer une chronologie des entrées et sorties
            $timeline = [];
            foreach ($rawTimestamps as $state => $entries) {
                if (is_string($entries)) {
                    // Ancien format - pas assez d'informations pour les transitions
                    continue;
                }

                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['enter'])) {
                            $timeline[] = [
                                'time' => $entry['enter'],
                                'state' => $state,
                                'type' => 'enter'
                            ];
                        }

                        if (isset($entry['exit']) && $entry['exit'] !== null) {
                            $timeline[] = [
                                'time' => $entry['exit'],
                                'state' => $state,
                                'type' => 'exit'
                            ];
                        }
                    }
                }
            }

            // Trier la chronologie par date
            usort($timeline, function($a, $b) {
                return strtotime($a['time']) - strtotime($b['time']);
            });

            // Compter les transitions
            $previousState = null;
            foreach ($timeline as $event) {
                if ($event['type'] === 'enter' && $previousState !== null) {
                    $transition = $previousState . ' -> ' . $event['state'];

                    if (!isset($transitions[$transition])) {
                        $transitions[$transition] = 0;
                    }

                    $transitions[$transition]++;
                }

                if ($event['type'] === 'exit') {
                    $previousState = $event['state'];
                }
            }
        }

        // Trier par nombre de transitions décroissant
        arsort($transitions);

        return $transitions;
    }

    /**
     * Calcule le nombre moyen de transitions par document
     */
    private function getAverageTransitionsPerDocument(array $documents): float
    {
        $totalTransitions = 0;
        $documentCount = 0;

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            $documentTransitions = 0;

            foreach ($rawTimestamps as $state => $entries) {
                if (is_array($entries)) {
                    $documentTransitions += count($entries);
                } else {
                    // Ancien format
                    $documentTransitions++;
                }
            }

            // Soustraire 1 car le premier état n'est pas une transition
            if ($documentTransitions > 0) {
                $documentTransitions--;
            }

            $totalTransitions += $documentTransitions;
            $documentCount++;
        }

        if ($documentCount === 0) {
            return 0;
        }

        return round($totalTransitions / $documentCount, 1);
    }

    /**
     * Analyse la répartition du temps dans le workflow
     */
    private function getWorkflowEfficiency(array $documents): array
    {
        // Définir des catégories d'états pour l'analyse
        $stateCategories = [
            'Création' => ['BE_0', 'BE_1', 'BE'],
            'Production' => ['Assembly', 'Machining', 'Molding', 'Methode_assemblage'],
            'Achats' => ['Achat_Rfq', 'Achat_RoHs_REACH', 'Achat_F30', 'Achat_FIA', 'Achat_Hts'],
            'Qualité' => ['Quality', 'Qual_Logistique', 'QProd'],
            'Logistique' => ['Logistique', 'Metro'],
            'Costing' => ['Costing', 'GID'],
            'Autres' => ['Produit', 'Planning', 'Core_Data', 'Project', 'Prod_Data', 'Saisie_hts', 'Indus', 'methode_Labo', 'Tirage_Plans']
        ];

        // Initialiser les compteurs
        $timeByCategory = [];
        foreach ($stateCategories as $category => $states) {
            $timeByCategory[$category] = 0;
        }

        $totalTime = 0;
        $totalDocuments = 0;
        $completedDocuments = 0;
        $totalCycleTime = 0;

        foreach ($documents as $document) {
            $rawTimestamps = $document->getRawStateTimestamps();
            if (!$rawTimestamps) {
                continue;
            }

            $totalDocuments++;
            $documentTotalTime = 0;
            $isCompleted = false;

            // Calculer le temps de cycle (de BE_0 à l'état final)
            $firstTimestamp = null;
            $lastTimestamp = null;

            foreach ($rawTimestamps as $state => $entries) {
                $totalDays = $document->getTotalDaysInState($state);

                if ($totalDays === null) {
                    continue;
                }

                $documentTotalTime += $totalDays;

                // Attribuer le temps à la catégorie correspondante
                foreach ($stateCategories as $category => $states) {
                    if (in_array($state, $states)) {
                        if (!isset($timeByCategory[$category])) {
                            $timeByCategory[$category] = 0;
                        }
                        $timeByCategory[$category] += $totalDays;
                        break;
                    }
                }

                // Trouver le premier et le dernier timestamp pour calculer le temps de cycle
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        $enterDate = new \DateTime($entry['enter']);
                        if ($firstTimestamp === null || $enterDate < $firstTimestamp) {
                            $firstTimestamp = $enterDate;
                        }

                        if ($entry['exit'] !== null) {
                            $exitDate = new \DateTime($entry['exit']);
                            if ($lastTimestamp === null || $exitDate > $lastTimestamp) {
                                $lastTimestamp = $exitDate;
                            }
                        }
                    }
                } elseif (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($firstTimestamp === null || $date < $firstTimestamp) {
                        $firstTimestamp = $date;
                    }
                    if ($lastTimestamp === null || $date > $lastTimestamp) {
                        $lastTimestamp = $date;
                    }
                }
            }

            $totalTime += $documentTotalTime;

            // Si le document a un premier et un dernier timestamp, calculer le temps de cycle
            if ($firstTimestamp !== null && $lastTimestamp !== null) {
                $cycleTime = $lastTimestamp->diff($firstTimestamp)->days;
                $totalCycleTime += $cycleTime;
                $completedDocuments++;
            }
        }

        // Calculer les pourcentages
        $percentageByCategory = [];
        foreach ($timeByCategory as $category => $time) {
            $percentageByCategory[$category] = $totalTime > 0 ? round(($time / $totalTime) * 100, 1) : 0;
        }

        // Calculer le temps de cycle moyen
        $averageCycleTime = $completedDocuments > 0 ? round($totalCycleTime / $completedDocuments, 1) : 0;

        $result = [
            'time_by_category' => $timeByCategory,
            'percentage_by_category' => $percentageByCategory,
            'total_time' => $totalTime,
            'average_cycle_time' => $averageCycleTime,
            'total_documents' => $totalDocuments,
            'completed_documents' => $completedDocuments
        ];

        return $result;
    }
}
