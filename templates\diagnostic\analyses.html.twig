{% extends 'base.html.twig' %}

{% block title %}Diagnostic des Analyses{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-stethoscope me-2"></i>
                        Diagnostic des Analyses et Prédictions
                    </h1>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Cette page diagnostique les problèmes dans le système d'analyses et de prédictions.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Problèmes identifiés -->
    {% if problems|length > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Problèmes Critiques Identifiés
                    </h4>
                </div>
                <div class="card-body">
                    {% for problem in problems %}
                    <div class="alert alert-{{ problem.severity == 'critical' ? 'danger' : 'warning' }} mb-3">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-{{ problem.severity == 'critical' ? 'times-circle' : 'exclamation-triangle' }} fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading">{{ problem.message }}</h5>
                                <p class="mb-0"><strong>Impact:</strong> {{ problem.impact }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Statistiques comparatives -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        Méthode Actuelle (Problématique)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-3 mb-3">
                                <div class="h2 text-warning mb-0">{{ current_method_stats.count }}</div>
                                <small class="text-muted">Documents analysés</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3 mb-3">
                                <div class="h2 text-warning mb-0">{{ current_method_stats.avg_time|round(1) }}</div>
                                <small class="text-muted">Jours (moyenne)</small>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <strong>Problème:</strong> Ne considère que les documents avec visa BE_0 ET Costing
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Méthode Améliorée
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-3 mb-3">
                                <div class="h2 text-success mb-0">{{ improved_stats.overall_stats.total_documents }}</div>
                                <small class="text-muted">Documents analysés</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3 mb-3">
                                <div class="h2 text-success mb-0">{{ improved_stats.overall_stats.overall_avg_time }}</div>
                                <small class="text-muted">Jours (moyenne)</small>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-success">
                        <strong>Avantage:</strong> Utilise les timestamps complets du workflow
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Couverture des données -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Couverture des Données
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <div class="h3 text-primary mb-0">{{ total_documents }}</div>
                                <small class="text-muted">Documents totaux</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <div class="h3 text-info mb-0">{{ visa_stats.with_be0_visa }}</div>
                                <small class="text-muted">Avec visa BE_0</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <div class="h3 text-info mb-0">{{ visa_stats.with_costing_visa }}</div>
                                <small class="text-muted">Avec visa Costing</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <div class="h3 text-{{ coverage_rate < 20 ? 'danger' : (coverage_rate < 50 ? 'warning' : 'success') }} mb-0">
                                    {{ coverage_rate|round(1) }}%
                                </div>
                                <small class="text-muted">Couverture</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="progress mt-3" style="height: 20px;">
                        <div class="progress-bar bg-{{ coverage_rate < 20 ? 'danger' : (coverage_rate < 50 ? 'warning' : 'success') }}" 
                             role="progressbar" 
                             style="width: {{ coverage_rate }}%"
                             aria-valuenow="{{ coverage_rate }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            {{ coverage_rate|round(1) }}% des documents analysés
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques par type de document (méthode améliorée) -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistiques par Type de Document (Méthode Améliorée)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Nombre</th>
                                    <th>Temps Moyen</th>
                                    <th>Temps Médian</th>
                                    <th>Min - Max</th>
                                    <th>Qualité</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc_type, stats in improved_stats.doc_type_stats %}
                                <tr>
                                    <td><span class="badge bg-primary">{{ doc_type }}</span></td>
                                    <td>{{ stats.count }}</td>
                                    <td>{{ stats.avg_time|default('N/A') }} jours</td>
                                    <td>{{ stats.median_time|default('N/A') }} jours</td>
                                    <td>
                                        {% if stats.min_time is defined and stats.max_time is defined %}
                                            {{ stats.min_time }} - {{ stats.max_time }} jours
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if stats.count < 10 %}
                                            <span class="badge bg-danger">Échantillon faible</span>
                                        {% elseif stats.count < 50 %}
                                            <span class="badge bg-warning">Échantillon moyen</span>
                                        {% else %}
                                            <span class="badge bg-success">Bon échantillon</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recommandations -->
    {% if improved_stats.recommendations|length > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Recommandations Automatiques
                    </h5>
                </div>
                <div class="card-body">
                    {% for recommendation in improved_stats.recommendations %}
                    <div class="alert alert-{{ recommendation.type }} mb-2">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-{{ recommendation.type == 'error' ? 'times-circle' : (recommendation.type == 'warning' ? 'exclamation-triangle' : 'info-circle') }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <strong>{{ recommendation.message }}</strong>
                                <br><small>{{ recommendation.suggestion }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Actions Recommandées
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ path('app_diagnostic_fix_recommendations') }}" class="btn btn-primary">
                            <i class="fas fa-wrench me-1"></i>
                            Voir les Solutions
                        </a>
                        <button id="refreshAnalysis" class="btn btn-secondary">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser l'Analyse
                        </button>
                        <a href="{{ path('app_forecast') }}" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line me-1"></i>
                            Retour aux Prévisions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('refreshAnalysis').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Actualisation...';
    location.reload();
});
</script>
{% endblock %}
