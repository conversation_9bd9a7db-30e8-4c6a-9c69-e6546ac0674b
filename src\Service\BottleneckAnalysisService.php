<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use App\Utils\DocumentConstants;

class BottleneckAnalysisService
{
    private DocumentRepository $documentRepository;
    private DataAnalysisService $dataAnalysisService;

    public function __construct(
        DocumentRepository $documentRepository,
        DataAnalysisService $dataAnalysisService
    ) {
        $this->documentRepository = $documentRepository;
        $this->dataAnalysisService = $dataAnalysisService;
    }

    /**
     * Analyse complète des goulots d'étranglement - Version optimisée
     */
    public function analyzeBottlenecks(): array
    {
        try {
            // Exécuter l'analyse des états une seule fois et réutiliser les résultats
            $stateAnalysis = $this->analyzeStateBottlenecks();
            $teamAnalysis = $this->analyzeTeamBottlenecksFromStateAnalysis($stateAnalysis);

            return [
                'state_analysis' => $stateAnalysis,
                'transition_analysis' => [], // Désactivé pour la performance
                'team_analysis' => $teamAnalysis,
                'document_type_analysis' => $this->analyzeDocumentTypeBottlenecks(),
                'temporal_analysis' => $this->analyzeTemporalBottlenecks(),
                'recommendations' => $this->generateBottleneckRecommendationsFromAnalysis($stateAnalysis, $teamAnalysis)
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Erreur lors de l\'analyse des goulots : ' . $e->getMessage(),
                'state_analysis' => [],
                'transition_analysis' => [],
                'team_analysis' => [],
                'document_type_analysis' => [],
                'temporal_analysis' => [],
                'recommendations' => []
            ];
        }
    }

    /**
     * Analyse des goulots par état - Version optimisée avec SQL
     */
    public function analyzeStateBottlenecks(): array
    {
        try {
            // Utiliser une requête SQL optimisée pour récupérer les données directement
            $stateStats = $this->documentRepository->getStateBottleneckAnalysis();

            // Calculer les statistiques avancées pour chaque état
            foreach ($stateStats as $state => &$stats) {
                if ($stats['document_count'] > 0) {
                    // avg_time est déjà calculé dans getStateBottleneckAnalysis()

                    // Récupérer les temps individuels pour les calculs statistiques
                    $times = $this->documentRepository->getProcessingTimesForState($state);
                    $stats['times'] = $times;
                    $stats['median_time'] = $this->calculateMedian($times);
                    $stats['std_deviation'] = $this->calculateStandardDeviation($times);
                    $stats['bottleneck_score'] = $this->calculateBottleneckScore($stats);
                    $stats['efficiency_rating'] = $this->calculateEfficiencyRating($stats['avg_time']);
                }
            }

            // Trier par score de goulot
            uasort($stateStats, function($a, $b) {
                return $b['bottleneck_score'] <=> $a['bottleneck_score'];
            });

            return $stateStats;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Analyse des goulots de transition entre états - Version optimisée
     */
    public function analyzeTransitionBottlenecks(): array
    {
        try {
            // Utiliser une requête SQL optimisée pour analyser les transitions
            $transitions = $this->documentRepository->getTransitionBottleneckAnalysis();

            // Calculer les statistiques pour chaque transition
            foreach ($transitions as &$transition) {
                if ($transition['count'] > 0) {
                    $transition['avg_time'] = $transition['total_time'] / $transition['count'];
                    $transition['median_time'] = $this->calculateMedian($transition['times']);
                    $transition['bottleneck_score'] = $this->calculateTransitionBottleneckScore($transition);
                }
            }

            // Trier par score de goulot
            uasort($transitions, function($a, $b) {
                return $b['bottleneck_score'] <=> $a['bottleneck_score'];
            });

            return array_slice($transitions, 0, 10); // Top 10 transitions problématiques
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Analyse des goulots par équipe/département
     */
    public function analyzeTeamBottlenecks(): array
    {
        // Appeler la version optimisée qui réutilise l'analyse des états
        $stateAnalysis = $this->analyzeStateBottlenecks();
        return $this->analyzeTeamBottlenecksFromStateAnalysis($stateAnalysis);
    }

    /**
     * Analyse des goulots par équipe/département - Version optimisée qui réutilise l'analyse des états
     */
    public function analyzeTeamBottlenecksFromStateAnalysis(array $stateAnalysis): array
    {
        try {
            // Mapping des états vers les équipes responsables
            $stateToTeam = [
                'BE_0' => 'Bureau d\'Études',
                'BE_1' => 'Bureau d\'Études',
                'BE' => 'Bureau d\'Études',
                'Quality' => 'Qualité',
                'Qual_Logistique' => 'Qualité',
                'Logistique' => 'Logistique',
                'Achat' => 'Achats',
                'Achat_F30' => 'Achats',
                'Achat_Hts' => 'Achats',
                'Achat_RoHs_REACH' => 'Achats',
                'Core_Data' => 'Données',
                'Prod_Data' => 'Production',
                'QProd' => 'Production',
                'methode_Labo' => 'Laboratoire',
                'Methode_assemblage' => 'Méthodes',
                'Tirage_Plans' => 'Plans',
                'Costing' => 'Costing'
            ];

            $teamStats = [];

            foreach ($stateAnalysis as $state => $stats) {
                $team = $stateToTeam[$state] ?? 'Autre';

                if (!isset($teamStats[$team])) {
                    $teamStats[$team] = [
                        'states' => [],
                        'total_time' => 0,
                        'total_documents' => 0,
                        'bottleneck_score' => 0
                    ];
                }

                $teamStats[$team]['states'][$state] = $stats;
                $teamStats[$team]['total_time'] += $stats['total_time'];
                $teamStats[$team]['total_documents'] += $stats['document_count'];
                $teamStats[$team]['bottleneck_score'] += $stats['bottleneck_score'];
            }

            // Calculer les moyennes par équipe
            foreach ($teamStats as &$team) {
                if ($team['total_documents'] > 0) {
                    $team['avg_time_per_document'] = $team['total_time'] / $team['total_documents'];
                    $team['avg_bottleneck_score'] = count($team['states']) > 0 ? $team['bottleneck_score'] / count($team['states']) : 0;
                }
            }

            // Trier par score de goulot moyen
            uasort($teamStats, function($a, $b) {
                return $b['avg_bottleneck_score'] <=> $a['avg_bottleneck_score'];
            });

            return $teamStats;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Analyse des goulots par type de document - Version optimisée
     */
    public function analyzeDocumentTypeBottlenecks(): array
    {
        try {
            $docTypes = ['ASSY', 'MACH', 'MOLD', 'DOC', 'PUR'];
            $typeAnalysis = [];

            // Récupérer toutes les analyses par type de document en une seule requête
            $docTypeStats = $this->documentRepository->getDocumentTypeBottleneckAnalysis($docTypes);

            foreach ($docTypes as $docType) {
                if (isset($docTypeStats[$docType])) {
                    $stats = $docTypeStats[$docType];
                    $typeAnalysis[$docType] = [
                        'avg_processing_time' => $stats['avg_processing_time'],
                        'total_documents' => $stats['total_documents'],
                        'min_time' => $stats['min_time'],
                        'max_time' => $stats['max_time'],
                        'bottleneck_score' => $this->calculateDocTypeBottleneckScore($stats['avg_processing_time'], []),
                        'state_distribution' => $stats['state_distribution'] ?? []
                    ];
                }
            }

            return $typeAnalysis;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Analyse temporelle des goulots - Version optimisée
     */
    public function analyzeTemporalBottlenecks(): array
    {
        try {
            // Récupérer l'analyse temporelle directement depuis la base de données
            $monthlyAnalysis = $this->documentRepository->getTemporalBottleneckAnalysis(6);

            return $monthlyAnalysis;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Génère des recommandations pour résoudre les goulots
     */
    public function generateBottleneckRecommendations(): array
    {
        $stateAnalysis = $this->analyzeStateBottlenecks();
        $teamAnalysis = $this->analyzeTeamBottlenecks();
        return $this->generateBottleneckRecommendationsFromAnalysis($stateAnalysis, $teamAnalysis);
    }

    /**
     * Génère des recommandations pour résoudre les goulots - Version optimisée qui réutilise les analyses
     */
    public function generateBottleneckRecommendationsFromAnalysis(array $stateAnalysis, array $teamAnalysis): array
    {
        $recommendations = [];

        // Recommandations par état
        foreach (array_slice($stateAnalysis, 0, 3) as $state => $stats) {
            if ($stats['bottleneck_score'] > 7) {
                $recommendations[] = [
                    'type' => 'state_bottleneck',
                    'priority' => 'high',
                    'state' => $state,
                    'issue' => "L'état '{$state}' présente un goulot majeur avec {$stats['avg_time']} jours en moyenne",
                    'actions' => $this->getStateRecommendations($state, $stats),
                    'expected_impact' => $this->calculateExpectedImpact($stats)
                ];
            }
        }

        // Recommandations par équipe
        foreach (array_slice($teamAnalysis, 0, 2) as $team => $stats) {
            if ($stats['avg_bottleneck_score'] > 6) {
                $recommendations[] = [
                    'type' => 'team_bottleneck',
                    'priority' => 'medium',
                    'team' => $team,
                    'issue' => "L'équipe '{$team}' présente des goulots sur plusieurs états",
                    'actions' => $this->getTeamRecommendations($team, $stats),
                    'expected_impact' => $this->calculateTeamImpact($stats)
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Calcule le score de goulot d'un état (0-10)
     */
    private function calculateBottleneckScore(array $stats): float
    {
        $avgTime = $stats['avg_time'];
        $variance = $stats['std_deviation'] ?? 0;
        $volume = $stats['document_count'];

        // Score basé sur temps moyen (0-5), variance (0-3), et volume (0-2)
        $timeScore = min(5, $avgTime / 2);
        $varianceScore = min(3, $variance / 3);
        $volumeScore = min(2, $volume / 50);

        return round($timeScore + $varianceScore + $volumeScore, 1);
    }

    /**
     * Calcule le score de goulot d'une transition
     */
    private function calculateTransitionBottleneckScore(array $transition): float
    {
        $avgTime = $transition['avg_time'];
        $frequency = $transition['count'];

        return round(($avgTime * 0.7) + ($frequency * 0.3), 1);
    }

    /**
     * Calcule le score de goulot d'un type de document
     */
    private function calculateDocTypeBottleneckScore(float $avgTime, array $stateStats): float
    {
        $maxStateScore = 0;
        foreach ($stateStats as $stats) {
            $maxStateScore = max($maxStateScore, $stats['bottleneck_score'] ?? 0);
        }

        return round(($avgTime * 0.4) + ($maxStateScore * 0.6), 1);
    }

    /**
     * Calcule la médiane d'un tableau
     */
    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);

        if ($count === 0) return 0;
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        } else {
            return $values[intval($count / 2)];
        }
    }

    /**
     * Calcule l'écart-type
     */
    private function calculateStandardDeviation(array $values): float
    {
        if (count($values) < 2) return 0;

        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);

        return sqrt($variance);
    }

    /**
     * Calcule le rating d'efficacité (A-F)
     */
    private function calculateEfficiencyRating(float $avgTime): string
    {
        if ($avgTime <= 2) return 'A';
        if ($avgTime <= 4) return 'B';
        if ($avgTime <= 6) return 'C';
        if ($avgTime <= 10) return 'D';
        return 'F';
    }

    /**
     * Extrait la séquence d'états d'un document
     */
    private function extractStateSequence(array $timestamps): array
    {
        $sequence = [];

        foreach ($timestamps as $state => $entries) {
            if (is_array($entries)) {
                foreach ($entries as $entry) {
                    if (isset($entry['enter'])) {
                        $sequence[] = [
                            'state' => $state,
                            'date' => new \DateTime($entry['enter'])
                        ];
                    }
                }
            }
        }

        // Trier par date
        usort($sequence, function($a, $b) {
            return $a['date'] <=> $b['date'];
        });

        return $sequence;
    }



    /**
     * Recommandations spécifiques par état
     */
    private function getStateRecommendations(string $state, array $stats): array
    {
        $recommendations = [
            'BE_0' => ['Augmenter les ressources BE', 'Automatiser la validation initiale', 'Prioriser les documents urgents'],
            'Quality' => ['Revoir les procédures qualité', 'Former l\'équipe', 'Paralléliser les contrôles'],
            'Achat' => ['Négocier avec les fournisseurs', 'Automatiser les commandes', 'Diversifier les sources']
        ];

        return $recommendations[$state] ?? ['Analyser les causes spécifiques', 'Optimiser les processus', 'Former les équipes'];
    }

    /**
     * Recommandations par équipe
     */
    private function getTeamRecommendations(string $team, array $stats): array
    {
        return [
            'Revoir la charge de travail de l\'équipe',
            'Identifier les besoins en formation',
            'Optimiser les outils et processus',
            'Considérer un renforcement temporaire'
        ];
    }

    /**
     * Calcule l'impact attendu d'une amélioration
     */
    private function calculateExpectedImpact(array $stats): string
    {
        $reduction = min(50, $stats['avg_time'] * 0.3);
        return "Réduction estimée de {$reduction}% du temps de traitement";
    }

    /**
     * Calcule l'impact attendu pour une équipe
     */
    private function calculateTeamImpact(array $stats): string
    {
        $improvement = min(30, $stats['avg_bottleneck_score'] * 3);
        return "Amélioration estimée de {$improvement}% de l'efficacité globale";
    }
}
